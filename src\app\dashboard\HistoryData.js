import React, { useState, useEffect } from 'react';


import Box from '@mui/material/Box';


import * as ConstructionData from './ConstructionData'
import * as DCData from './DCData'
import { Calendar } from 'primereact/calendar';
import 'primereact/resources/primereact.css';
import 'primereact/resources/themes/lara-light-blue/theme.css';
import 'primeicons/primeicons.css'; //icons
import DashboardLocationFilter from './DashboardLocationFilter';

import Observation from './Observation';
import moment from 'moment';
import API from '../services/API';
import { ALL_INCIDENT_URL, ALL_REPORT_DATA_URL, OBSERVATION_REPORT_URL } from '../constants';

import { infoData } from './data'
import { Tabs, Tab, Typography } from '@mui/material';
import { Row, Col } from 'react-bootstrap';
import Eptw from './Eptw';
import EHS from './EHS';
import { useSelector } from 'react-redux';
import InspectionTab from './InspectionTab';
import AuditTab from './AuditTab';
import IncidentTab from './IncidentTab';
import { Dropdown } from 'primereact/dropdown';

import YearlyDataChart from './YearlyDataChart';
import MonthlyYearlyDataChart from './MonthlyYearlyDataChart';
import RangeMonthlyYearlyDataChart from './RangeMonthlyYearlyDataChart';



const HistoryData = () => {
    const incidentOptions = [
        { label: 'All Incidents', value: 'all' },
        { label: 'Recordable Incidents', value: 'recordable' },
        { label: 'Non-recordable Incidents', value: 'non-recordable' }
    ];

    // Example state hooks (if not defined)
    const [incidentType, setIncidentType] = useState('all'); // default to 'all'
    function transformApiResponse(apiResponseArray, incidentDataArray = []) {
        const yearToInclude = 2025;

        // Step 1: Create a map of unique keys from incidentDataArray
        const incidentKeys = new Set();
        incidentDataArray.forEach(inc => {
            const key = [
                inc.year,
                inc.month,
                inc.locationOne.name,
                inc.locationThree.name,
                inc.locationFour.name
            ].join("|");

            if (inc.year === yearToInclude) {
                incidentKeys.add(key);
            }
        });

        // Step 2: Create a set of existing keys in apiResponseArray
        const existingApiResponseKeys = new Set();
        apiResponseArray.forEach(api => {
            const [monthStr, yearStr] = api.yearAndMonth.split(" ");
            const month = moment().month(monthStr.toLowerCase()).format("M") * 1;
            const year = parseInt(yearStr);

            const key = [
                year,
                month,
                api.locationOne?.name || "",
                api.locationThree?.name || "",
                api.locationFour?.name || ""
            ].join("|");

            if (year === yearToInclude) {
                existingApiResponseKeys.add(key);
            }
        });

        // Step 3: Create dummy API responses for missing combinations
        const missingApiResponses = Array.from(incidentKeys)
            .filter(key => !existingApiResponseKeys.has(key))
            .map(key => {
                const [year, month, locationOneName, locationThreeName, locationFourName] = key.split("|");

                return {
                    yearAndMonth: `${moment().month(month - 1).format("MMM")} ${year}`,  // <-- FIXED
                    locationOne: { name: locationOneName },
                    locationThree: { name: locationThreeName },
                    locationFour: { name: locationFourName },
                    numberOfEmployees: 0,
                    workingDaysOfEmployee: 0,
                    numberofContractors: 0,
                    workingDaysOfContractors: 0,
                    dailyHoursOfEmployee: 0,
                    dailyHoursOfContractors: 0,
                    noOfSafety: 0,
                    noOfToolbox: 0,
                    noOfEhsTraining: 0,
                    noOfInspection: 0,
                    noOfManagmentSiteWalk: 0,
                    noOfAuthority: 0,
                    noOfSafeObservation: 0,
                    noOfRiskObservation: 0
                };
            });

        // Step 4: Merge real and dummy responses
        const fullApiResponseArray = [...apiResponseArray, ...missingApiResponses];

        // Step 5: Apply your original transformation
        const finalResponse = fullApiResponseArray
            .filter(apiResponse => parseInt(apiResponse.yearAndMonth.split(" ")[1]) === yearToInclude)
            .map(apiResponse => {
                const [monthStr, yearStr] = apiResponse.yearAndMonth.split(" ");
                const month = monthStr.toLowerCase();
                const year = parseInt(yearStr);

                const locationOneName = apiResponse.locationOne?.name || "";
                const locationThreeName = apiResponse.locationThree?.name || "";
                const locationFourName = apiResponse.locationFour?.name || "";

                const country = locationOneName.match(/\(([^)]+)\)/)?.[1] || "Unknown";

                let bu = "Other";
                if (locationThreeName.includes("Construction")) bu = "Construction";
                else if (locationThreeName.includes("DC") || locationThreeName.includes("Data Center")) bu = "DC";
                else if (locationThreeName.includes("Office")) bu = "Office";

                const site = locationFourName.replace(/\s+/g, "_") || "Unknown_Site";

                const incidentsForMonth = incidentDataArray.filter(inc =>
                    inc.year === year &&
                    inc.month === moment().month(month).format("M") * 1 &&
                    inc.locationOne.name === locationOneName &&
                    inc.locationThree.name === locationThreeName &&
                    inc.locationFour.name === locationFourName
                );

                let recordableIncidentCount = 0;

                const incidentCounts = {
                    noOfFatality: 0,
                    noOfMedicalTreatmentCases: 0,
                    noOfFirstAidCases: 0,
                    noOfLossOfConsciousnessCases: 0,
                    authorityReportableIncident: 0,
                    noticesOrStopWorkOrders: 0,
                    noOfDaysAwayFromWorkCasesLTICases: 0,
                    noOfRestrictedWorkCasesLightDutyJobTransfer: 0,
                    noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence: 0
                };

                incidentsForMonth.forEach(incident => {
                    if (incident.fatality === "Yes") incidentCounts.noOfFatality++;
                    if (incident.medicalTreatment === "Yes") incidentCounts.noOfMedicalTreatmentCases++;
                    if (incident.firstAid === "Yes") incidentCounts.noOfFirstAidCases++;
                    if (incident.lossOfConscious === "Yes") incidentCounts.noOfLossOfConsciousnessCases++;
                    if (incident.reportAuthority === "Yes") incidentCounts.authorityReportableIncident++;
                    if (incident.stopWorkOrder === "Yes") incidentCounts.noticesOrStopWorkOrders++;
                    if (incident.lostTime === "Yes") incidentCounts.noOfDaysAwayFromWorkCasesLTICases++;
                    if (incident.informationStep?.modifiedWork === true) incidentCounts.noOfRestrictedWorkCasesLightDutyJobTransfer++;
                    if (incident.dangerousOccurance === "Yes") incidentCounts.noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence++;
                    if (/Level [2-5]/.test(incident.actualImpact)) {
                        recordableIncidentCount++;
                    }
                });

                return {
                    year,
                    month,
                    country,
                    bu,
                    level: "site",
                    site,
                    averageNoOfSTTGdcEmployeesPerDay: apiResponse.numberOfEmployees / (apiResponse.workingDaysOfEmployee || 1),
                    averageNoOfContractorEmployeesPerDay: apiResponse.numberofContractors / (apiResponse.workingDaysOfContractors || 1),
                    totalNoOfEmployees: apiResponse.numberOfEmployees + apiResponse.numberofContractors,
                    monthlyHoursWorked: (apiResponse.dailyHoursOfEmployee || 0) +
                        (apiResponse.dailyHoursOfContractors || 0),
                    cumulativeWorkHours: (apiResponse.dailyHoursOfEmployee * apiResponse.workingDaysOfEmployee || 0) +
                        (apiResponse.dailyHoursOfContractors * apiResponse.workingDaysOfContractors || 0),
                    noOfSafetyInductionsConducted: apiResponse.noOfSafety || 0,
                    noOfToolboxMeetingsSafetyBriefingsSafeStarts: apiResponse.noOfToolbox || 0,
                    noOfEHSTrainings: apiResponse.noOfEhsTraining || 0,
                    noOfEHSInspectionsAudits: apiResponse.noOfInspection || 0,
                    noOfManagementSiteWalkInspection: apiResponse.noOfManagmentSiteWalk || 0,
                    authorityNGOUnionVisits: apiResponse.noOfAuthority || 0,
                    noOfSafeObservations: apiResponse.noOfSafeObservation || 0,
                    noOfAtRiskObservations: apiResponse.noOfRiskObservation || 0,
                    totalNoOfObservations: (apiResponse.noOfSafeObservation || 0) + (apiResponse.noOfRiskObservation || 0),
                    ...incidentCounts,
                    noOfRecordableIncidentCases: recordableIncidentCount
                };
            });

        return finalResponse;
    }



    const [rdata, setRdata] = useState([]);
    const [isLoaded, setIsLoaded] = useState(false);
    useEffect(() => {
        getReportData();
        getIncidentData();
    }, [])

    const getReportData = async () => {

        const params = {
            "include": [{ "relation": "locationOne" }, { "relation": "locationTwo" }, { "relation": "locationThree" }, { "relation": "locationFour" }]

        };
        const response = await API.get(`${ALL_REPORT_DATA_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        const incidentData = await getIncidentData();
        if (response.status === 200) {
            const data = response.data.filter(i => i.type === 'monthly');
            console.log(incidentData, 'incidentData')
            const modifiedData = transformApiResponse(data, incidentData);



            setRdata(modifiedData)
            setIsLoaded(true)

        }
    }

    const getIncidentData = async () => {
        const incidentParams = {
            include: [
                { relation: "user" },
                { relation: "incidentOwner" },
                { relation: "investigator" },
                { relation: "reviewer" },
                { relation: "locationOne" },
                { relation: "locationTwo" },
                { relation: "locationThree" },
                { relation: "locationFour" }
            ]
        };

        try {
            const incidentResponse = await API.get(
                `${ALL_INCIDENT_URL}?filter=${encodeURIComponent(JSON.stringify(incidentParams))}`
            );

            if (incidentResponse.status === 200) {
                const incidentData = incidentResponse.data
                    .filter(item => {
                        const date = moment(item.incidentDate, 'DD/MM/YYYY hh:mm A');
                        return date.year() === 2025;
                    })
                    .map(item => {
                        const date = moment(item.incidentDate, 'DD/MM/YYYY hh:mm A');
                        return {
                            ...item,
                            month: date.month() + 1,
                            year: date.year()
                        };
                    });

                return incidentData;
            }
        } catch (error) {
            console.error("Error fetching incident data:", error);
            return [];
        }
    };



    const me = useSelector((state) => state.login.user)

    const [info, setInfo] = useState([])
    const [activeTab, setActiveTab] = useState(0);




    const [selectedYear, setSelectedYear] = useState(2024);
    const [selectedYearData, setSelectedYearData] = useState(null);




    const [totalObservation, setTotalObservation] = useState(0)
    const [obsData, setObsData] = useState([]);
    const [obsFilterData, setObsFilterData] = useState([]);
    const [selectedOption, setSelectedOption] = useState('construction'); // default to 'construction'
    const [data, setData] = useState(ConstructionData);
    const [date, setDate] = useState(new Date(2024, 2));
    const [filteredData, setFilteredData] = useState([]);
    const [filteredMonthData, setFilteredMonthData] = useState([]);
    const [selectedMonthData, setSelectedMonthData] = useState({});
    const [dateRange, setDateRange] = useState(null);
    const [manHours, setManHours] = useState({
        chart1: 200000,
        chart2: 200000,
        chart3: 200000,
        chart4: 200000,
        chart5: 200000,
        chart6: 200000,
        chart7: 200000,
        chart8: 200000
        // Add more as needed for each chart
    });


    // Handle contractor selection

    const handleManHoursChange = (chartId, value) => {

        console.log(chartId, value)
        setManHours(current => ({
            ...current,
            [chartId]: value,
        }));
    };

    const [filterCriteria, setFilterCriteria] = useState({
        countries: [],
        buLevels: [],
        sites: []
    });

    // Handler function to update filter criteria based on selections from DashboardLocationFilter
    const handleSelectionChange = ({ countries, buLevels, sites }) => {

        setFilterCriteria({ countries, buLevels, sites });


    };



    useEffect(() => {
        if (isLoaded && rdata.length > 0 && infoData.length > 0) { // Ensure both are present before executing
            const combinedData = [...infoData, ...rdata];

            const filteredData = combinedData.filter(item => {
                const isCountrySelected = filterCriteria.countries.some(country => country.id === item.country);
                const isBUSelected = filterCriteria.buLevels.some(bu => bu.id === item.bu);
                const isSiteSelected = item.site ? filterCriteria.sites.some(site => site.id === item.site) : true;


                return isCountrySelected && isBUSelected && isSiteSelected;
            });

            setInfo(filteredData);
        }
    }, [filterCriteria, rdata, infoData, isLoaded]); // Depend on all required data


    useEffect(() => {
        if (date) {
            const selectedDateMoment = moment(date);
            // Filter data for the selected month
            const modifiedData = preprocessData(info)

            const selectedMonth = modifiedData.find(item => {
                const itemDateMoment = moment(`${item.year}-${item.month}`, "YYYY-MMM");
                return itemDateMoment.isSame(selectedDateMoment, 'month');
            });
            setSelectedMonthData(selectedMonth || {});

            // Filter the data array to include only months within the past 12 months from the selected date
            const filtered = info.filter(item => {
                const itemDateMoment = moment(`${item.year}-${item.month}`, "YYYY-MMM");
                return itemDateMoment.isSameOrBefore(selectedDateMoment, 'month') &&
                    itemDateMoment.isAfter(selectedDateMoment.clone().subtract(12, 'months'));
            });

            setFilteredData(filtered);

            const filteredMonthData = info.filter(item => {
                const itemDateMoment = moment(`${item.year}-${item.month}`, "YYYY-MMM");
                return itemDateMoment.year() === selectedDateMoment.year() &&
                    itemDateMoment.isSameOrBefore(selectedDateMoment, 'month');
            });

            setFilteredMonthData(filteredMonthData);
        }
    }, [date, info]);

    useEffect(() => {
        if (selectedYear && info.length > 0) {
            const yearData = info.filter(item => item.year === selectedYear);

            setSelectedYearData(yearData)
        }
    }, [selectedYear, info]);





    useEffect(() => {
        getObservationData();


    }, [])

    const getObservationData = async () => {

        const params = {
            "include": [{ "relation": "submitted" }, { "relation": "locationFour" }]

        };
        const response = await API.get(`${OBSERVATION_REPORT_URL}?filter=${encodeURIComponent(JSON.stringify(params))}`);
        if (response.status === 200) {
            const preprocessedData = response.data.map(item => ({
                ...item,
                'submitted.firstName': item.submitted ? item.submitted.firstName : '',
                'color': item.type === 'Safe' ? 'None' : item.status === "At Risk - Closed" ? 'None' : moment().isAfter(moment(item.dueDate, 'DD-MM-YYYY')) ? 'Overdue' : 'Upcoming',
                created: moment(item.created).format('Do MMM YYYY hh:mm A'),
                'newStatus': item.status === "At Risk - Closed" ? "Reported & Closed" : item.status === "At Risk - Actions Assigned" ? "Actions Assigned" : item.status === "At Risk - Actions to be Verified" ? "Actions Verified" : item.status === "At Risk - Actions to be Verified" ? "Actions Taken - Pending Verification" : item.status === "Safe - Closed" ? "Reported & Closed" : item.status

            }));

            setObsData(preprocessedData)
            setObsFilterData(preprocessedData)

        }
    }




    useEffect(() => {
        if (activeTab === 6) {
            // Set default to Jan 2025 (From), leave To as null
            setDateRange([new Date(2025, 0, 1), new Date()]);
        } else {
            // Default range: last 12 months
            const today = new Date();
            const lastYear = new Date();
            lastYear.setFullYear(today.getFullYear(), today.getMonth() - 11);
            setDateRange([lastYear, today]);
        }
    }, [activeTab]);




    useEffect(() => {
        if (selectedOption === 'construction') {
            setData(ConstructionData);
        } else {
            setData(DCData);
        }
    }, [selectedOption]);

    const handleFilter = (data) => {

    };



    const calculateTRIR = (data, manHours) => {

        let cumulativeIncidents = 0;
        let cumulativeHours = 0;
        let cumulativeFatalities = 0;
        let cumulativeLTIFR = 0;
        let cumulativeHCIR = 0; // For accumulating High Consequence Injury Rate

        return data && data.map(({
            year,
            month,
            monthlyHoursWorked,
            cumulativeWorkHours,
            noOfFatality,
            noOfRecordableIncidentCases,
            noOfDaysAwayFromWorkCasesLTICases,
            noOfRestrictedWorkCasesLightDutyJobTransfer,
            noOfLossOfConsciousnessCases,
            noOfMedicalTreatmentCases,
            noOfHealthRelatedCases
        }) => {

            const totalMonthlyHCIR = noOfDaysAwayFromWorkCasesLTICases + noOfRestrictedWorkCasesLightDutyJobTransfer + noOfLossOfConsciousnessCases + noOfMedicalTreatmentCases + noOfHealthRelatedCases;
            cumulativeHCIR += totalMonthlyHCIR;

            cumulativeIncidents += noOfRecordableIncidentCases;
            cumulativeFatalities += noOfFatality;
            cumulativeLTIFR += noOfDaysAwayFromWorkCasesLTICases;
            cumulativeHours += monthlyHoursWorked; // Assuming cumulativeWorkHours is correctly cumulative in your data

            const monthlyTRIR = monthlyHoursWorked > 0 ? (noOfRecordableIncidentCases / monthlyHoursWorked) * manHours : 0;
            const cumulativeTRIR = cumulativeHours > 0 ? (cumulativeIncidents / cumulativeHours) * manHours : 0;

            const monthlyFatalityRate = monthlyHoursWorked > 0 ? (noOfFatality / monthlyHoursWorked) * manHours : 0;
            const cumulativeFatalityRate = cumulativeHours > 0 ? (cumulativeFatalities / cumulativeHours) * manHours : 0;

            const monthlyLTIFR = monthlyHoursWorked > 0 ? (noOfDaysAwayFromWorkCasesLTICases / monthlyHoursWorked) * manHours : 0;
            const cumulativeLTIFRRate = cumulativeHours > 0 ? (cumulativeLTIFR / cumulativeHours) * manHours : 0;

            const monthlyHCIR = monthlyHoursWorked > 0 ? (totalMonthlyHCIR / monthlyHoursWorked) * manHours : 0;
            const cumulativeHCIRRate = cumulativeHours > 0 ? (cumulativeHCIR / cumulativeHours) * manHours : 0;


            return {
                year,
                month,
                monthlyTRIR: parseFloat(monthlyTRIR.toFixed(2)),
                cumulativeTRIR: parseFloat(cumulativeTRIR.toFixed(2)),
                monthlyFatalityRate: parseFloat(monthlyFatalityRate.toFixed(2)),
                cumulativeFatalityRate: parseFloat(cumulativeFatalityRate.toFixed(2)),
                monthlyLTIFR: parseFloat(monthlyLTIFR.toFixed(2)),
                cumulativeLTIFR: parseFloat(cumulativeLTIFRRate.toFixed(2)),
                monthlyHCIR: parseFloat(monthlyHCIR.toFixed(2)),
                cumulativeHCIR: parseFloat(cumulativeHCIRRate.toFixed(2)),
                totalMonthlyHCIR, // Added to return the total HCIR for each month
                noOfRecordableIncidentCases,
                noOfFatality,
                noOfDaysAwayFromWorkCasesLTICases
            };
        });
    };

    function preprocessData(data) {



        const aggregatedData = {};

        data && data.forEach(entry => {

            const monthNumber = moment(entry.month, 'MMM').format('MM');
            const key = `${entry.year}-${entry.month}`; // Unique key for each month of a year

            if (!aggregatedData[key]) {
                // Initialize with the first entry of the month
                aggregatedData[key] = { ...entry, month: monthNumber };
            } else {
                // Aggregate by summing relevant fields
                aggregatedData[key].averageNoOfSTTGdcEmployeesPerDay += entry.averageNoOfSTTGdcEmployeesPerDay;
                aggregatedData[key].averageNoOfContractorEmployeesPerDay += entry.averageNoOfContractorEmployeesPerDay;
                aggregatedData[key].totalNoOfEmployees += entry.totalNoOfEmployees;
                aggregatedData[key].monthlyHoursWorked += entry.monthlyHoursWorked;
                aggregatedData[key].cumulativeWorkHours += entry.cumulativeWorkHours;
                // Assuming cumulativeWorkHours is correctly cumulative in your data, it might not need aggregation cumulativeWorkHours
                aggregatedData[key].noOfSafetyInductionsConducted += entry.noOfSafetyInductionsConducted;
                aggregatedData[key].noOfToolboxMeetingsSafetyBriefingsSafeStarts += entry.noOfToolboxMeetingsSafetyBriefingsSafeStarts;
                aggregatedData[key].noOfEHSTrainings += entry.noOfEHSTrainings;
                aggregatedData[key].noOfEHSInspectionsAudits += entry.noOfEHSInspectionsAudits;
                aggregatedData[key].noOfManagementSiteWalkInspection += entry.noOfManagementSiteWalkInspection;
                aggregatedData[key].authorityNgoUnionVisits += entry.authorityNgoUnionVisits;
                aggregatedData[key].noOfSafeObservations += entry.noOfSafeObservations;
                aggregatedData[key].noOfAtRiskObservations += entry.noOfAtRiskObservations;
                aggregatedData[key].totalNoOfObservations += entry.totalNoOfObservations;
                aggregatedData[key].noOfFatality += entry.noOfFatality;
                aggregatedData[key].noOfDaysAwayFromWorkCasesLTICases += entry.noOfDaysAwayFromWorkCasesLTICases;
                aggregatedData[key].noOfRestrictedWorkCasesLightDutyJobTransfer += entry.noOfRestrictedWorkCasesLightDutyJobTransfer;
                aggregatedData[key].noOfLossOfConsciousnessCases += entry.noOfLossOfConsciousnessCases;
                aggregatedData[key].noOfMedicalTreatmentCases += entry.noOfMedicalTreatmentCases;
                aggregatedData[key].noOfHealthRelatedCases += entry.noOfHealthRelatedCases;
                aggregatedData[key].noOfRecordableIncidentCases += entry.noOfRecordableIncidentCases;
                aggregatedData[key].legalAndOtherNonCompliances += entry.legalAndOtherNonCompliances;
                aggregatedData[key].noticesOrStopWorkOrders += entry.noticesOrStopWorkOrders;
                aggregatedData[key].authorityReportableIncident += entry.authorityReportableIncident;
                aggregatedData[key].noOfNearMissCases += entry.noOfNearMissCases;
                aggregatedData[key].noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence += entry.noOfSeriousPotentiallySeriousIncidentsDangerousOccurrence;
                aggregatedData[key].noOfFirstAidCases += entry.noOfFirstAidCases;
            }
        });

        // Convert the aggregated object back into an array
        const dataArray = Object.values(aggregatedData);

        // Sort the array by year and then by month (numerically)
        dataArray.sort((a, b) => {
            const yearComparison = a.year - b.year;
            if (yearComparison !== 0) {
                return yearComparison;
            }
            // Now 'month' is a numerical value, sort accordingly
            return parseInt(a.month, 10) - parseInt(b.month, 10);
        });

        // Ensure the month is converted back to its original abbreviation form for the final output, if needed
        dataArray.forEach(item => {
            item.month = moment(item.month, 'MM').format('MMM'); // Convert back to abbreviation
        });

        return dataArray;
    }
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };
    function TabPanel(props) {
        const { children, value, index, ...other } = props;

        return (
            <div
                role="tabpanel"
                hidden={value !== index}
                id={`tabpanel-${index}`}
                aria-labelledby={`tab-${index}`}
                {...other}
            >
                {value === index && (
                    <Box p={3}>
                        <Typography>{children}</Typography>
                    </Box>
                )}
            </div>
        );
    }

    if (me.type === 'External') {
        return ''
    }



    return (

        <>
            <div className='row'>
                <h4 className='mb-0'><strong>Monthly EHS Performance Dashboard</strong></h4>
                {/* <p>Detailed Progress of monthly KPIs</p> */}

                <div className='my-3'></div>
            </div>

            <div className="row d-flex align-items-center justify-content-between">
                {/* Location Filter */}
                <div className="col-4">
                    <div className="form-group">
                        <DashboardLocationFilter
                            rData={rdata}
                            onSelectionChange={handleSelectionChange}
                            handleFilter={handleFilter}
                        />
                    </div>
                </div>

                {/* Date Range Picker */}
                {activeTab !== 0 && (
                    <div className="col-5 d-flex gap-3 align-items-end">
                        <div className="form-group">
                            <label>From</label>
                            <br />
                            <Calendar
                                value={
                                    dateRange && dateRange[0]
                                        ? dateRange[0]
                                        : activeTab === 6
                                            ? new Date(2025, 0, 1)
                                            : null
                                }
                                onChange={(e) =>
                                    setDateRange([e.value, dateRange ? dateRange[1] : null])
                                }
                                view="month"
                                dateFormat="MM yy"
                                yearNavigator
                                yearRange="2023:2025"
                                placeholder="Select From Month/Year"
                                maxDate={dateRange ? dateRange[1] : null}
                                minDate={activeTab === 6 ? new Date(2025, 0, 1) : null}
                                disabled={activeTab === 0}
                            />
                        </div>
                        <div className="form-group">
                            <label>To</label>
                            <br />
                            <Calendar
                                value={dateRange ? dateRange[1] : null}
                                onChange={(e) =>
                                    setDateRange([dateRange ? dateRange[0] : null, e.value])
                                }
                                view="month"
                                dateFormat="MM yy"
                                yearNavigator
                                yearRange="2023:2025"
                                placeholder="Select To Month/Year"
                                minDate={dateRange ? dateRange[0] : null}
                                disabled={activeTab === 0}
                            />
                        </div>
                    </div>
                )}

                {/* Incident Type Dropdown */}
                {activeTab === 6 && (
                    <div className="col-3">
                        <div className="form-group">
                            <label>Incident Type</label>
                            <Dropdown
                                value={incidentType}
                                options={incidentOptions}
                                onChange={(e) => setIncidentType(e.value)}
                                placeholder="Select Incident Type"
                                className="w-100"
                            />
                        </div>
                    </div>
                )}
            </div>

            <Row className="mb-4">
                <Col>
                    <Tabs value={activeTab} onChange={handleTabChange} aria-label="Dashboard Tabs">
                        <Tab label="Overview" />
                        <Tab label="Observations" />
                        <Tab label="Permit to Work" />
                        <Tab label="EHS Statistics" />
                        <Tab label="Inspection" />
                        <Tab label="Audit" />
                        <Tab label="Incident" />

                    </Tabs>
                </Col>
            </Row>

            {activeTab === 0 && (<TabPanel value={activeTab} index={0}>

                <div className='row '>
                    <div className='col-md-12 mb-5'>
                        <MonthlyYearlyDataChart
                            initialDate={new Date()}
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={2}
                            manHoursKey={manHours.chart2}
                            chartConfig={{
                                title: "12 Month Rolling TRIR",
                                doubleAxis: true,
                                barColor: '#D91669',
                                barLegend: 'No of Incidents',
                                lineLegend: '12 Month Rolling TRIR',
                                barKey: "noOfRecordableIncidentCases",
                                lineKey: "rollingTRIR",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>
                    <div className='col-md-12 mb-5'>
                        <YearlyDataChart
                            initialYear={2025}
                            data={info} // pass the full data array
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={1}
                            manHoursKey={manHours.chart1}
                            chartConfig={{
                                title: "Yearly Cumulative TRIR",
                                doubleAxis: true,
                                barColor: '#cc8a3b',
                                barLegend: 'No of Incidents',
                                lineLegend: 'Yearly Cumulative TRIR',
                                barKey: "noOfRecordableIncidentCases",
                                lineKey: "cumulativeTRIR",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>

                    <div className='col-md-12 mb-5'>

                        <RangeMonthlyYearlyDataChart
                            initialStartDate={new Date(2024, 3)} // January 2024
                            initialEndDate={new Date()} // Current month
                            data={info} // Your data array
                            preprocessData={preprocessData} // Data preprocessing function
                            calculateTRIR={calculateTRIR} // Function to calculate TRIR
                            chartId={3}
                            manHoursKey={manHours.chart3}
                            chartConfig={{
                                title: "Monthly TRIR",
                                doubleAxis: false,
                                barColor: '#034b7b',
                                barLegend: 'No of Incidents',
                                lineLegend: 'Monthly TRIR',
                                barKey: "noOfRecordableIncidentCases",
                                lineKey: "monthlyTRIR",
                                onManHoursChange: handleManHoursChange
                            }}


                        />
                    </div>

                </div>

                <div className='row '>
                    <div className='col-md-12 mb-5'>
                        <MonthlyYearlyDataChart
                            initialDate={new Date()} // Adjust initial date as necessary
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={4}
                            manHoursKey={manHours.chart4}
                            chartConfig={{
                                title: "12 Month Rolling Fatality Rate",
                                doubleAxis: true,
                                barColor: '#D91669',
                                barLegend: 'No of Fatality',
                                lineLegend: 'Cumulative Fatality Rate',
                                barKey: "noOfFatality",
                                lineKey: "cumulativeFatalityRate",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>
                    <div className='col-md-12 mb-5'>
                        <YearlyDataChart
                            initialYear={2025}
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={5}
                            manHoursKey={manHours.chart5}
                            chartConfig={{
                                title: "12 Month Cumulative Fatality Rate",
                                doubleAxis: true,
                                barColor: '#cc8a3b',
                                barLegend: 'No of Fatality',
                                lineLegend: '12 Month Cumulative Fatality Rate',
                                barKey: "noOfFatality",
                                lineKey: "cumulativeFatalityRate",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>
                    <div className='col-md-12 mb-5'>

                        <RangeMonthlyYearlyDataChart
                            initialStartDate={new Date(2024, 3)} // January 2024
                            initialEndDate={new Date()} // Current month
                            data={info} // Your data array
                            preprocessData={preprocessData} // Data preprocessing function
                            calculateTRIR={calculateTRIR} // Function to calculate TRIR
                            chartId={6}
                            manHoursKey={manHours.chart6}
                            chartConfig={{
                                title: "Monthly Fatality Rate",
                                doubleAxis: false,
                                barColor: '#034b7b',
                                barLegend: 'Monthly Fatality Rate',
                                lineLegend: 'Cumulative Fatality Rate',
                                barKey: "monthlyFatalityRate",
                                lineKey: "cumulativeFatalityRate",
                                onManHoursChange: handleManHoursChange
                            }}


                        />
                    </div>

                </div>


                <div className='row'>
                    <div className='col-md-12 mb-5'>
                        <MonthlyYearlyDataChart
                            initialDate={new Date()} // Adjust initial date as necessary
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={7}
                            manHoursKey={manHours.chart7}
                            chartConfig={{
                                title: "12 Month Rolling LTIFR",
                                doubleAxis: true,
                                barColor: '#D91669',
                                barLegend: 'No of Days Away from Work Cases',
                                lineLegend: 'Cumulative LTIFR',
                                barKey: "noOfDaysAwayFromWorkCasesLTICases",
                                lineKey: "cumulativeLTIFR",
                                onManHoursChange: handleManHoursChange
                            }}
                        />

                    </div>
                    <div className='col-md-12 mb-5'>
                        <YearlyDataChart
                            initialYear={2025}
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={8}
                            manHoursKey={manHours.chart8}
                            chartConfig={{
                                title: "12 Month Cumulative LTIFR",
                                doubleAxis: true,
                                barColor: '#cc8a3b',
                                barLegend: 'No of Days Away from Work Cases',
                                lineLegend: '12 Month Cumulative LTIFR',
                                barKey: "noOfDaysAwayFromWorkCasesLTICases",
                                lineKey: "cumulativeLTIFR",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>
                    <div className='col-md-12  mb-5'>

                        <RangeMonthlyYearlyDataChart
                            initialStartDate={new Date(2024, 3)} // January 2024
                            initialEndDate={new Date()} // Current month
                            data={info} // Your data array
                            preprocessData={preprocessData} // Data preprocessing function
                            calculateTRIR={calculateTRIR} // Function to calculate TRIR
                            chartId={9}
                            manHoursKey={manHours.chart9}
                            chartConfig={{
                                title: "Monthly LTIFR: Lost Time Incident Frequency Rate (incl. Fatalities)",
                                doubleAxis: false,
                                barColor: '#034b7b',
                                barLegend: 'Monthly LTIFR',
                                lineLegend: 'Cumulative LTIFR',
                                barKey: "monthlyLTIFR",
                                lineKey: "cumulativeLTIFR",
                                onManHoursChange: handleManHoursChange
                            }}


                        />
                    </div>

                </div>

                <div className='row '>
                    <div className='col-md-12 mb-5'>
                        <MonthlyYearlyDataChart
                            initialDate={new Date()} // Adjust initial date as necessary
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={10}
                            manHoursKey={manHours.chart10}
                            chartConfig={{
                                title: "12 Month Rolling HCIR (excl. Fatalities)",
                                doubleAxis: true,
                                barColor: '#D91669',
                                barLegend: 'No of HCIR',
                                lineLegend: 'Cumulative HCIR',
                                barKey: "totalMonthlyHCIR",
                                lineKey: "cumulativeHCIR",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>
                    <div className='col-md-12 mb-5'>
                        <YearlyDataChart
                            initialYear={2025}
                            data={info}
                            preprocessData={preprocessData}
                            calculateTRIR={calculateTRIR}
                            chartId={11}
                            manHoursKey={manHours.chart11}
                            chartConfig={{
                                title: "12 Month Cumulative HCIR (excl. Fatalities)",
                                doubleAxis: true,
                                barColor: '#cc8a3b',
                                barLegend: 'No of HCIR',
                                lineLegend: '12 Month Cumulative HCIR',
                                barKey: "totalMonthlyHCIR",
                                lineKey: "cumulativeHCIR",
                                onManHoursChange: handleManHoursChange
                            }}
                        />
                    </div>
                    <div className='col-md-12  mb-5'>

                        <RangeMonthlyYearlyDataChart
                            initialStartDate={new Date(2024, 3)} // January 2024
                            initialEndDate={new Date()} // Current month
                            data={info} // Your data array
                            preprocessData={preprocessData} // Data preprocessing function
                            calculateTRIR={calculateTRIR} // Function to calculate TRIR
                            chartId={12}
                            manHoursKey={manHours.chart12}
                            chartConfig={{
                                title: "Monthly HCIR: High-Consequence Injury Rate (excl. Fatalities)",
                                doubleAxis: false,
                                barColor: '#034b7b',
                                barLegend: 'Monthly HCIR',
                                lineLegend: 'Cumulative HCIR',
                                barKey: "monthlyHCIR",
                                lineKey: "cumulativeHCIR",
                                onManHoursChange: handleManHoursChange
                            }}


                        />
                    </div>

                </div>

            </TabPanel>)}

            {activeTab === 1 && (<TabPanel value={activeTab} index={1}>
                <Observation dateRange={dateRange} filterCriteria={filterCriteria} />
            </TabPanel>)}

            {activeTab === 2 && (<TabPanel value={activeTab} index={2}>
                <Eptw dateRange={dateRange} filterCriteria={filterCriteria} />
            </TabPanel>)}

            {activeTab === 3 && (<TabPanel value={activeTab} index={3}>
                <EHS dateRange={dateRange} filterCriteria={filterCriteria} />
            </TabPanel>)}

            {activeTab === 4 && (<TabPanel value={activeTab} index={4}>
                <InspectionTab />
            </TabPanel>)}
            {activeTab === 5 && (<TabPanel value={activeTab} index={5}>
                <AuditTab />
            </TabPanel>)}
            {activeTab === 6 && (<TabPanel value={activeTab} index={6}>
                <IncidentTab dateRange={dateRange} filterCriteria={filterCriteria} />
            </TabPanel>)}




        </>

    )
}

export default HistoryData;