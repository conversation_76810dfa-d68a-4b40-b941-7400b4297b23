import React, { useEffect, useState, useCallback } from 'react';
import 'primereact/resources/themes/saga-blue/theme.css';  //theme
import 'primereact/resources/primereact.min.css';          //core css
import 'primeicons/primeicons.css';                        //icons
import { Container, Row, Col, Card, Nav, Tab } from 'react-bootstrap';
// import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

import Water<PERSON>hart from './WaterChart';
import BarOne from './BarOne';
import WaterConsumption from './WaterConsumption';
import GoalTargetsComponent from './GoalTargetsComponent';
import BarFour from './BarFour';
import BarFive from './BarFive';
import ObservationDashTable from './ObservationDashTable';
import API from '../services/API';
import { OBS_CATEGORY_MONTH, OBS_CATEGORY_STATS, OBS_DETAILED_RESULTS, OBS_REPORT_PROCESS, OBS_TOP_GHS_MONTH, OBS_TOP_GHS_TYPE, OBS_TOP_REPORTERS, OBS_TYPE_COND_MONTH, OBS_TYPE_COND_STATS } from '../constants';
import CategoryPieChart from './CategoryPieChart';
import ObservationPieChart from './ObservationPieChart';
import StackedBarChart from './StackedBarChart';
import GhsStackedBarChart from './GhsStackedBarChart';
import HorizontalStackedBarChart from './HorizontalStackedBarChart';
import MonthlyRollingChart from './MonthlyRollingChart';
import TopReportersChart from './TopReportersChart';
import ObservationOther from '../pages/ObservationOther';
import moment from 'moment';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend } from 'chart.js';

import { getDisplayDateRange } from './dateUtils';

// ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend);


function Observation({ dateRange }) {

  const displayDateRange = getDisplayDateRange(dateRange);

  const [type, setType] = useState('')
  const [process, setProcess] = useState(null)
  const [processData, setProcessData] = useState([])
  const [loadingDetails, setLoadingDetails] = useState(false)
  const [category, setCategory] = useState(null)
  const [typeCond, setTypeCond] = useState(null)
  const [typeCondMonth, setTypeCondMonth] = useState(null)
  const [ghsType, setGhsType] = useState(null)
  const [ghsMonth, setGhsMonth] = useState(null)
  const [categoryMonth, setCategoryMonth] = useState(null)
  const [people, setPeople] = useState(null)

  useEffect(() => {
    getProcessData()
    getCategoryStats()
    getTypeCond()
    getTypeCondMonth()
    getGhsType()
    getGhsMonth()
    getCategoryMonth()
    getPeople()

  }, [])

  const getProcessData = async () => {
    const response = await API.post(OBS_REPORT_PROCESS);
    if (response.status === 200) {
      setProcess(response.data)
    }
  }

  const getCategoryStats = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString()); // Convert to ISO format
      try {
        const response = await API.post(OBS_CATEGORY_STATS, {
          from,
          to,
        });
        if (response.status === 200) {
          setCategory(response.data);
        }
      } catch (error) {
        console.error("Error fetching category stats:", error);
      }
    } else {
      console.error("Invalid dateRange provided");
    }
  };


  const getTypeCond = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString());
      const response = await API.post(OBS_TYPE_COND_STATS, { from, to });
      if (response.status === 200) {
        setTypeCond(response.data);
      }
    } else {
      console.error("Invalid dateRange provided for getTypeCond");
    }
  };

  const getTypeCondMonth = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString());
      const response = await API.post(OBS_TYPE_COND_MONTH, { from, to });
      if (response.status === 200) {
        setTypeCondMonth(response.data);
      }
    } else {
      console.error("Invalid dateRange provided for getTypeCondMonth");
    }
  };

  const getGhsType = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString());
      const response = await API.post(OBS_TOP_GHS_TYPE, { from, to });
      if (response.status === 200) {
        setGhsType(response.data);
      }
    } else {
      console.error("Invalid dateRange provided for getGhsType");
    }
  };

  const getGhsMonth = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString());
      const response = await API.post(OBS_TOP_GHS_MONTH, { from, to });
      if (response.status === 200) {
        setGhsMonth(response.data);
      }
    } else {
      console.error("Invalid dateRange provided for getGhsMonth");
    }
  };

  const getCategoryMonth = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString());
      const response = await API.post(OBS_CATEGORY_MONTH, { from, to });
      if (response.status === 200) {
        setCategoryMonth(response.data);
      }
    } else {
      console.error("Invalid dateRange provided for getCategoryMonth");
    }
  };

  const getPeople = async () => {
    if (dateRange && dateRange.length === 2) {
      const [from, to] = dateRange.map((date) => new Date(date).toISOString());
      const response = await API.post(OBS_TOP_REPORTERS, { from, to });
      if (response.status === 200) {
        setPeople(response.data);
      }
    } else {
      console.error("Invalid dateRange provided for getPeople");
    }
  };

  const preprocessData = useCallback((data) => {
    return data.map(item => {
      let conditionAct = '';
      try {
        const parsedRemarks = JSON.parse(item?.remarks || '{}');
        conditionAct = parsedRemarks?.condition_act ? `(${parsedRemarks.condition_act})` : '';
      } catch (error) {
        console.error('Error parsing remarks for item:', item, error);
      }

      const lastActionOwner = getLastActionOwner(item?.actions);

      return {
        ...item,
        'submitted.firstName': item?.submitted?.firstName || '',
        'color': setColor(item),
        'created': moment(item?.created).format('Do MMM YYYY'),
        'newStatus': item?.rectifiedStatus === 'Yes' ? 'Reported & Rectified on Spot' : displayStatus(item?.status),
        'type': `${item?.type || ''}${conditionAct}`,
        'closeDate': getCloseActionDate(item),
        'dueDate': lastActionOwner?.dueDate || ''
      };
    });
  }, []);

  const getDetailedResults = useCallback(async (resultType) => {
    setLoadingDetails(true);
    try {
      const typeMapping = {
        1: 'closedOnTime',
        2: 'overdue',
        3: 'rectifiedOnSpot',
        4: 'firstInstanceClosure'
      };

      const payload = {
        type: typeMapping[resultType]
      };

      // Add date range if available
      if (dateRange && dateRange.length === 2) {
        const [from, to] = dateRange.map((date) => new Date(date).toISOString());
        payload.from = from;
        payload.to = to;
      }

      const response = await API.post(OBS_DETAILED_RESULTS, payload);
      if (response.status === 200) {
        setProcessData(preprocessData(response.data.data || []));
      }
    } catch (error) {
      console.error("Error fetching detailed results:", error);
      setProcessData([]);
    } finally {
      setLoadingDetails(false);
    }
  }, [dateRange, preprocessData]);


  const displayStatus = (status) => {

    let returnText = '';

    switch (status) {

      case "At Risk - Closed":
        returnText = 'Reported & Closed'

        break;
      case "At Risk - Actions Assigned":
        returnText = 'Actions Assigned'

        break;
      case "At Risk - Actions Re-Assigned":
        returnText = 'Actions Re-Assigned'

        break;
      case "At Risk - Actions to be Verified":
        returnText = 'Actions Taken - Pending Verification'

        break;
      case "Safe - Closed":
        returnText = 'Reported & Closed'

        break;
      default:
        returnText = status
        break;

    }

    return returnText;

  }



  const getLastActionOwner = (items) => {
    // Iterate from the last item to the first
    for (let i = items?.length - 1; i >= 0; i--) {
      if (items[i].actionType === 'action_owner') {
        return items[i];
      }
    }
    // If no match is found, return null or any default value
    return null;
  };

  const setColor = (item) => {
    if (
      item.type === 'Safe' ||
      item.status === 'At Risk - Closed' ||
      item.status === 'Approved' ||
      item.dueDate === ''
    ) {
      return 'None';
    }

    // Parse dueDate
    const dueDate = moment(item.dueDate, [moment.ISO_8601, 'DD-MM-YYYY'], true);

    if (!dueDate.isValid()) {
      return 'None'; // Handle invalid dates
    }

    const today = moment().startOf('day');

    if (today.isSame(dueDate, 'day')) {
      return 'Due Soon';
    } else if (today.isAfter(dueDate, 'day')) {
      return 'Overdue';
    } else if (today.isBefore(dueDate, 'day')) {
      return 'Upcoming';
    }

    return 'None';
  };

  const getCloseActionDate = (item) => {




    if (item.status === 'At Risk - Closed' || item.status === 'Action Verified - Closed' || item.status === 'Reported & Closed') {
      if (item.actions) {
        const last = item.actions[item.actions.length - 1]

        return moment(last.createdDate).format('Do MMM YYYY')
      }






    } else {
      return ''
    }



  }

  useEffect(() => {
    if (type) {
      getDetailedResults(type);
    } else {
      setProcessData([]);
    }
  }, [type, dateRange]);



  const styles = {
    cardContainer: {
      width: '100%',
      display: 'flex',
      justifyContent: 'space-between',
      padding: '20px',
      height: '100%'
    },
    textContent: {
      flex: 1
    },
    emissionsValue: {
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '5px'
    },
    targetText: {
      color: 'black',
      fontWeight: 'normal',
      fontSize: '16px'
    },
    greenText: {
      color: 'green',
      fontWeight: 'bold'
    },
    redText: {
      color: 'red',
      fontWeight: 'bold'
    },
    chartContainer: {
      // width: 150,
      // display: 'flex',
      // justifyContent: 'center',
      // alignItems: 'center',
      // height: '100%'
    }
  };
  const emissionsData = [
    { month: 'Jan', emission: 32000 },
    { month: 'Feb', emission: 16000 },
    { month: 'Mar', emission: 32000 },
    { month: 'Apr', emission: 10000 },
    { month: 'May', emission: 42000 },
    { month: 'Jun', emission: 2000 },
    { month: 'Jul', emission: 25000 },
    { month: 'Aug', emission: 30000 },
    { month: 'Sep', emission: 22000 },
    { month: 'Oct', emission: 31000 },
    { month: 'Nov', emission: 18000 },
    { month: 'Dec', emission: 40000 },
  ];

  const lineChartData = {
    labels: emissionsData.map((data) => data.month),
    datasets: [
      {
        label: 'Emissions',
        data: emissionsData.map((data) => data.emission),
        borderColor: 'green', // Line color
        borderWidth: 2, // Thickness of the line
        pointBackgroundColor: 'green', // Color of data points
        pointRadius: 4, // Size of data points
        tension: 0.4, // Makes the line smoother
        fill: false, // Ensures no gradient or fill under the line
      },
    ],
  };

  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Monthly Emissions',
      },
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Month',
        },
      },
      y: {
        title: {
          display: true,
          text: 'Emissions (kg)',
        },
        beginAtZero: true,
      },
    },
  };

  return (
    <>
      <Row className="mb-4">
        <Col md={3} onClick={() => setType(prevType => (prevType === 1 ? '' : 1))}>
          <Card className={`cursor-pointer ${type === 1 ? 'selected-card' : ''}`}>
            <Card.Body>
              <div style={{ height: '100%' }}>
                <div style={styles.targetText}>
                  Observations closed within stipulated time
                </div>
                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                  {process && process.closedOnTimeCount}
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3} onClick={() => setType(prevType => (prevType === 2 ? '' : 2))}>
          <Card className={`cursor-pointer ${type === 2 ? 'selected-card' : ''}`}>
            <Card.Body>
              <div style={{ height: '100%' }}>
                <div style={styles.targetText}>
                  Observations overdue for closure
                </div>
                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                  {process && process.overdueCount}
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3} onClick={() => setType(prevType => (prevType === 3 ? '' : 3))}>
          <Card className={`cursor-pointer ${type === 3 ? 'selected-card' : ''}`}>
            <Card.Body>
              <div style={{ height: '100%' }}>
                <div style={styles.targetText}>
                  Observations rectified on the spot
                </div>
                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                  {process && process.rectifiedOnSpotCount}
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3} onClick={() => setType(prevType => (prevType === 4 ? '' : 4))}>
          <Card className={`cursor-pointer ${type === 4 ? 'selected-card' : ''}`}>
            <Card.Body>
              <div style={{ height: '100%' }}>
                <div style={styles.targetText}>
                  First-instance closure rate of observations
                </div>
                <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                  {process && process.firstInstanceClosureRate.toFixed(2)}
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col md="12">
          {type !== '' && (
            loadingDetails ? (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <div>Loading detailed results...</div>
              </div>
            ) : (
              <ObservationDashTable obsdata={processData} />
            )
          )}
        </Col>
      </Row>

      <br />
      <Row className="mb-4">
        <Col md={6}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>Breakdown of observations by Category | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <CategoryPieChart info={category} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
        <Col md={6}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>Breakdown of observations by Nature | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <ObservationPieChart info={typeCond} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <br />
      <Row>
        <Col md={12}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>12 month rolling breakdown by nature of observations | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <StackedBarChart info={typeCondMonth} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <br />
      <Row>
        <Col md={12}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>Top 5 Observation by GMS Category | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <GhsStackedBarChart info={ghsType} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <br />
      <Row>
        <Col md={12}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>Monthly Top 5 GMS Category | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <HorizontalStackedBarChart info={ghsMonth} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <br />
      <Row>
        <Col md={12}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>12 Month Rolling Observation By Category | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <MonthlyRollingChart info={categoryMonth} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <br />
      <Row>
        <Col md={12}>
          <Card >
            <Card.Body>
              <h5 className='font-weight-bold'>Top Observation Reporters | {displayDateRange} </h5>
              {/* <p>Cupiditate tempore aliquid voluptatem est numquam distinctio dolor corporis cupiditate. Voluptatibus.</p> */}
              <TopReportersChart info={people} dateRange={dateRange} />
            </Card.Body>
          </Card>
        </Col>
      </Row>
      <br />

    </>
  );
}

export default Observation;
