import React, { useEffect, useState } from 'react';
import 'primereact/resources/themes/saga-blue/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import { Row, Col, Card } from 'react-bootstrap';
import API from '../services/API';
import AllPermit from '../pages/AllPermits';
import WaterChart from './WaterChart';
import BarOne from './BarOne';
import HighRiskActivityLineChart from './HighRiskActivityLineChart';
import BreakdownActivityLineChart from './BreakdownActivityLineChart';
import { getDisplayDateRange } from './dateUtils';

function Eptw({ dateRange }) {

    const displayDateRange = getDisplayDateRange(dateRange);
    const [chartData, setChartData] = useState(null);
    const [type, setType] = useState('');
    const [firstTime, setFirstTime] = useState([]);
    const [revoked, setRevoked] = useState([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await API.post('/permits/statistics');
                setChartData(response.data);
                setFirstTime(response.data.detailedResults.firstTimeApprovedPermits);
                setRevoked(response.data.detailedResults.revokedPermits);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };

        fetchData();
    }, []);

    const handleCardClick = (selectedType) => {
        setType(prevType => (prevType === selectedType ? '' : selectedType));
    };

    return (
        <>
            <Row className="mb-4">
                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 1 ? 'selected-card' : ''}`}
                        onClick={() => handleCardClick(1)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        {chartData?.firstTimeApprovalRate || 'N/A'}
                                    </div>
                                    <div>First time approval rate of permit applications</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 2 ? 'selected-card' : ''}`}
                        onClick={() => handleCardClick(2)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        {chartData?.revokedCount || 'N/A'}
                                    </div>
                                    <div>Permits revoked due to violation of identified controls</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 3 ? 'selected-card' : ''}`}
                        onClick={() => handleCardClick(3)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        0
                                    </div>
                                    <div>No. of at-risk observations in work activities under permit</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>

                <Col md={3}>
                    <Card
                        className={`cursor-pointer ${type === 4 ? 'selected-card' : ''}`}
                        onClick={() => handleCardClick(4)}
                    >
                        <Card.Body className="d-flex flex-column">
                            <div className='d-flex justify-content-between' style={{ flexGrow: 1 }}>
                                <div>
                                    <div style={{ marginTop: '10px', fontSize: '26px', fontWeight: 'bold' }}>
                                        0
                                    </div>
                                    <div>No. of safety incidents in work activities under permit</div>
                                </div>
                            </div>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col md="12">
                    {type !== '' && (
                        <AllPermit
                            data={type === 1 ? firstTime : type === 2 ? revoked : []}
                            from="dash"
                        />
                    )}
                </Col>
            </Row>

            <Row className="mb-4">
                <Col md={6}>
                    <Card className="">
                        <Card.Body>
                            <h5 className='font-weight-bold'>Breakdown of Permit Types | {displayDateRange}</h5>
                            <WaterChart type={'eptw'} />  {/* /permits/type-distribution */}

                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6}>
                    <Card className="">
                        <Card.Body>
                            <h5 className="font-weight-bold">Risk Profile of ePermit-to-Work Activities | {displayDateRange}</h5>
                            <HighRiskActivityLineChart dateRange={dateRange} />  {/*  /permits/risk-profile */}
                        </Card.Body>
                    </Card>

                </Col>
            </Row>

            <Row className="mb-4">
                <Col md={12}>
                    <Card className="mb-4">
                        <Card.Body>
                            <h5 className='font-weight-bold'>Rolling Data Trend of Permit Applications | {displayDateRange} </h5>
                            <BarOne type={'eptw'} dateRange={dateRange} />   {/* /permits/type-distribution-by-month */}
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={12}>
                    <Card className="">
                        <Card.Body>
                            <h5 className="font-weight-bold">High-Risk Activity Breakdown | {displayDateRange}</h5>
                            <BreakdownActivityLineChart dateRange={dateRange} />   {/* /permits/high-risk-breakdown */}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </>
    );
}

export default Eptw;
